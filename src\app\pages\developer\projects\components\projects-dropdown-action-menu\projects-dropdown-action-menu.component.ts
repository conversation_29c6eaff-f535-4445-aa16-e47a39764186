import { Component, OnInit, HostBinding, Input } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-projects-dropdown-action-menu',
  templateUrl: './projects-dropdown-action-menu.component.html',
  styleUrl: './projects-dropdown-action-menu.component.scss',
})
export class ProjectsDropdownActionMenuComponent implements OnInit {
  @HostBinding('class') class =
    'menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px';
  @HostBinding('attr.data-kt-menu') dataKtMenu = 'true';

  @Input() id: number | any;

  constructor(public translationService: TranslationService) {}

  ngOnInit(): void {}

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'ACTIONS': 'الإجراءات',
        'UPDATE': 'تحديث',
        'ARCHIVE': 'أرشفة',

      },
      'en': {
        'ACTIONS': 'Actions',
        'UPDATE': 'Update',
        'ARCHIVE': 'Archive',

      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
