import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { RequestService } from '../../../services/request.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { Page } from 'src/app/models/page.model';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-request-recommendations',
  templateUrl: './request-recommendations.component.html',
  styleUrls: ['./request-recommendations.component.scss'],
})
export class RequestRecommendationsComponent implements OnInit, OnDestroy {
  //from session
  brokerId: any;
  request: any = null;
  requestId: string | null = null;
  private routeSub: Subscription | null = null;
  private requestSub: Subscription | null = null;

  page: Page = new Page();
  recommendedUnits: any;
  selectedUnits: any[] = [];

  isReplied = false;

  // Sorting properties
  orderBy: string = 'id';
  orderDir: string = 'desc';

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;

    if (this.route.parent) {
      this.routeSub = this.route.parent.paramMap.subscribe((params) => {
        this.requestId = params.get('id') || this.requestService.getRequestId();
        console.log('RequestRecommendationsComponent - Request ID:', this.requestId);

        if (this.requestId) {
          this.page.pageNumber = 0;
          this.page.limit = 10;
          this.page.size = environment.TABLE_LIMIT;

          this.requestSub = this.requestService.getRequest().subscribe((request) => {
            this.request = request;
            console.log('RequestRecommendationsComponent - Request Data from Service:', this.request);
            this.cd.detectChanges();

            this.getRecommendedUnits(this.page);

            if (!this.request) {
              this.fetchRequest();
            }
          });
        } else {
          console.error('RequestRecommendationsComponent - No request ID found');
          Swal.fire('Invalid request ID.', '', 'error');
        }
      });
    } else {
      this.routeSub = null;
      this.requestId = this.requestService.getRequestId();
      console.error('RequestRecommendationsComponent - Parent route not found, fallback requestId:', this.requestId);
      if (this.requestId) {
        this.page.pageNumber = 0;
        this.page.limit = 10;
        this.page.size = environment.TABLE_LIMIT;

        this.requestSub = this.requestService.getRequest().subscribe((request) => {
          this.request = request;
          console.log('RequestRecommendationsComponent - Request Data from Service:', this.request);
          this.cd.detectChanges();

          this.getRecommendedUnits(this.page);

          if (!this.request) {
            this.fetchRequest();
          }
        });
      } else {
        console.error('RequestRecommendationsComponent - No request ID available');
        Swal.fire('Invalid request ID.', '', 'error');
      }
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    if (this.requestSub) {
      this.requestSub.unsubscribe();
    }
  }

  fetchRequest() {
    if (this.requestId) {
      this.requestService.getRequestById(this.requestId).subscribe({
        next: (response: any) => {
          this.request = response.data;
          this.requestService.setRequest(this.request);
          console.log('RequestRecommendationsComponent - Fetched Request Data:', this.request);
          this.cd.detectChanges();
        },
        error: (error: any) => {
          console.error('RequestRecommendationsComponent - Error fetching request:', error);
          this.cd.detectChanges();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  isUnitSelected(id: number): boolean {
    return this.selectedUnits.includes(id);
  }

  onUnitCheckboxChange(event: Event, unitId: any): void {
    const checkbox = event.target as HTMLInputElement;
    if (checkbox.checked) {
      this.selectedUnits.push(unitId);
    } else {
      this.selectedUnits = this.selectedUnits.filter((u) => u !== unitId);
    }
    console.log('Selected Units:', this.selectedUnits);
  }

  toggleAllUnits(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      this.selectedUnits = this.recommendedUnits.map((unit: any) => unit.id);
    } else {
      this.selectedUnits = [];
    }
    console.log('Selected Units:', this.selectedUnits);
  }

  get isAllSelected(): boolean {
    return this.recommendedUnits?.length > 0 && this.selectedUnits.length === this.recommendedUnits.length;
  }

  getRecommendedUnits(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    if (this.requestId) {
      this.requestService.getRecommendedUnits(this.requestId, this.brokerId, this.page).subscribe({
        next: (response: any) => {
          console.log('Recommended Units:', response.data);
          this.recommendedUnits = response.data;

          this.page.totalElements = response.count;
          this.page.count = Math.ceil(response.count / this.page.size);

          this.cd.markForCheck();
        },
        error: (error: any) => {
          console.error('Error fetching recommended units:', error);
          this.cd.markForCheck();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  formatAccessories(accessories: string[]): string {
    return accessories
      .map(item => item.replace(/_/g, ' '))
      .join(', ');
  }

  selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
    this.selectedUnitPlanImage = imgPath;

    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  onPageChange(newPageNumber: number)
  {
    this.page.pageNumber = newPageNumber;
    this.getRecommendedUnits(this.page);
  }


  makeReply()
  {
    this.requestService.makeReply(this.requestId, this.brokerId, this.selectedUnits).subscribe(
      (response:any) => {
        console.log(response.data);
        this.isReplied = true;
        Swal.fire('request reply is success', '', 'success');

        this.cd.markForCheck();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

   sortData(column: string) {
     if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

     this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.page.pageNumber = 0;
    this.getRecommendedUnits(this.page);
  }

   getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'UNIT_RECOMMENDATIONS': 'توصيات الوحدات',
        'SELECT_UNITS': 'اختر الوحدات',
        'SEND_REPLY': 'إرسال الرد',
        'UNIT_NUMBER': 'رقم الوحدة',
        'FLOOR': 'الطابق',
        'PROPERTY_NUMBER': 'رقم العقار',
        'AREA': 'المساحة',
        'ROOMS': 'الغرف',
        'BATHROOMS': 'الحمامات',
        'VIEW': 'الإطلالة',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'FINISHING_STATUS': 'حالة التشطيب',
        'UNIT_PLAN': 'مخطط الوحدة',
        'UNIT_LOCATION_MASTER_PLAN': 'موقع الوحدة في المخطط الرئيسي',
        'PRICE_PER_METER_CASH': 'السعر للمتر نقداً',
        'PRICE_PER_METER_INSTALLMENT': 'السعر للمتر بالتقسيط',
        'TOTAL_PRICE_CASH': 'إجمالي السعر نقداً',
        'TOTAL_PRICE_INSTALLMENT': 'إجمالي السعر بالتقسيط',
        'OTHER_ACCESSORIES': 'الإكسسوارات الأخرى',
        'ACTIONS': 'الإجراءات',
        'SELECT': 'اختيار',
        'LOADING': 'جاري التحميل...',
        'NO_UNITS_FOUND': 'لا توجد وحدات'
      },
      'en': {
        'UNIT_RECOMMENDATIONS': 'Unit Recommendations',
        'SELECT_UNITS': 'Select Units',
        'SEND_REPLY': 'Send Reply',
        'UNIT_NUMBER': 'Unit Number',
        'FLOOR': 'Floor',
        'PROPERTY_NUMBER': 'Property Number',
        'AREA': 'Area',
        'ROOMS': 'Rooms',
        'BATHROOMS': 'Bathrooms',
        'VIEW': 'View',
        'DELIVERY_DATE': 'Delivery Date',
        'FINISHING_STATUS': 'Finishing Status',
        'UNIT_PLAN': 'Unit Plan',
        'UNIT_LOCATION_MASTER_PLAN': 'Unit Location in Master Plan',
        'PRICE_PER_METER_CASH': 'Price Per Meter in Cash',
        'PRICE_PER_METER_INSTALLMENT': 'Price Per Meter in Installment',
        'TOTAL_PRICE_CASH': 'Total Price Cash',
        'TOTAL_PRICE_INSTALLMENT': 'Total Price Installment',
        'OTHER_ACCESSORIES': 'Other Accessories',
        'ACTIONS': 'Actions',
        'SELECT': 'Select',
        'LOADING': 'Loading...',
        'NO_UNITS_FOUND': 'No units found'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

}
