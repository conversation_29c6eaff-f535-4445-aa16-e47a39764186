// Contract Requests Dropdown Action Menu Styles
:host {
  min-width: 200px;
}

.dropdown-item {
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.cursor-pointer {
  cursor: pointer;
}

.text-success {
  color: #198754 !important;
}

.text-danger {
  color: #dc3545 !important;
}

// RTL Support for Dropdown Action Menu
:host-context(html[lang="ar"]) {
  direction: rtl;
  text-align: right;

  .dropdown-header {
    font-family: 'Noto <PERSON> Arabic', sans-serif !important;
    text-align: right !important;
    direction: rtl !important;
    font-weight: bold;
  }

  .dropdown-item {
    text-align: right !important;
    direction: rtl !important;
    font-family: 'Hacen Liner Screen St', sans-serif !important;
    padding: 0.5rem 1rem !important;

    &:hover {
      background-color: #f8f9fa !important;
      text-align: right !important;
    }

    i {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }
  }

  .dropdown-divider {
    margin: 0.5rem 0;
  }
}

// Enhanced English support - same styling as Arabic
:host-context(html[lang="en"]) {
  direction: ltr;
  text-align: left;
  min-width: 200px;

  .dropdown-header {
    font-family: inherit !important;
    text-align: left !important;
    direction: ltr !important;
    font-weight: 600 !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem;
    color: #181c32;
    margin-bottom: 0;
  }

  .dropdown-item {
    text-align: left !important;
    direction: ltr !important;
    font-family: inherit !important;
    padding: 0.5rem 1rem !important;
    white-space: nowrap !important;
    font-size: 0.875rem;
    color: #5e6278;
    transition: all 0.15s ease;
    border: none;
    background: none;
    width: 100%;
    display: flex;
    align-items: center;

    &:hover,
    &:focus {
      background-color: #f5f8fa !important;
      color: #181c32 !important;
      text-align: left !important;
    }

    i {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .dropdown-divider {
    margin: 0.5rem 0;
    border-color: #e4e6ef;
  }
}
