<div class="table-responsive">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <th class="w-25px ps-4 rounded-start">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
              data-kt-check-target=".widget-13-check" />
          </div>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('user_id')">
          {{ getTranslatedText('REQUEST_USER') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("user_id")
            }}</span>
        </th>
        <th class="min-w-150px">{{ getTranslatedText('REQUEST_OWNER') }}</th>
        <th class="min-w-100px cursor-pointer" (click)="sortData('title')">
          {{ getTranslatedText('REQUEST_TITLE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("title")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('created_at')">
          {{ getTranslatedText('REQUEST_DATE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("created_at")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('specialization_scope')">
          {{ getTranslatedText('SPECIALIZATION_SCOPE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("specialization_scope")
            }}</span>
        </th>
        <th class="min-w-100px cursor-pointer" (click)="sortData('type')">
          {{ getTranslatedText('REQUEST_TYPE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("type")
            }}</span>
        </th>
        <th class="min-w-100px cursor-pointer" (click)="sortData('status')">
          {{ getTranslatedText('STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("status")
            }}</span>
        </th>
        <th class="min-w-100px text-end rounded-end pe-4">{{ getTranslatedText('ACTIONS') }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let request of rows">
        <td class="ps-4">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input widget-13-check" type="checkbox" value="1" />
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px me-5">
              <img src="{{ request.user.image }}" alt="" class="rounded-circle" />
            </div>
            <div class="d-flex justify-content-start flex-column">
              <a class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                {{ request.user.name }}
              </a>
            </div>
          </div>
        </td>
        <td>
          <span *ngIf="request.user.role !== 'Client'" class="badge badge-light-primary fs-6">
            {{ getTranslatedText(request.user.role.toUpperCase()) }}
          </span>
          <span *ngIf="request.user.role === 'Client'" class="badge badge-light-info fs-6">
            {{ getTranslatedText('CLIENT') }}
          </span>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="d-flex justify-content-start flex-column">
              <a [routerLink]="['/requests/render', request.id]"
                class="text-gray-900 text-hover-dark-blue fw-bold fs-6">
                {{ request.title }}
              </a>
            </div>
          </div>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ request.createdAt }}
          </span>
        </td>
        <td>
          <span class="badge badge-dark-blue fs-6">
            {{ request.specializationScope }}
          </span>
        </td>
        <td>
          <span *ngIf="request.type === 'Purchasing'" class="badge badge-light-danger fs-6">
            {{ getTranslatedText('PURCHASING') }}
          </span>
          <span *ngIf="request.type === 'Sell'" class="badge badge-light-info fs-6">
            {{ getTranslatedText('SELL') }}
          </span>
          <span *ngIf="request.type === 'Rent_in'" class="badge badge-light-mid-blue fs-6">
            {{ getTranslatedText('RENT_IN') }}
          </span>
          <span *ngIf="request.type === 'Rent_out'" class="badge badge-light-dark-blue fs-6">
            {{ getTranslatedText('RENT_OUT') }}
          </span>
        </td>
        <td>
          <span *ngIf="request.status === 'new'" class="badge badge-light-warning fs-6">
            {{ getTranslatedText('NEW') }}
          </span>
          <span *ngIf="request.status === 'in_processing'" class="badge badge-light-mid-blue fs-6">
            {{ getTranslatedText('IN_PROCESSING') }}
          </span>
          <span *ngIf="request.status === 'finished'" class="badge badge-light-success fs-6">
            {{ getTranslatedText('FINISHED') }}
          </span>
        </td>
        <td [class.text-end]="translationService.getCurrentLanguage() !== 'ar'"
          [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
          [class.pe-4]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ps-4]="translationService.getCurrentLanguage() === 'ar'">
          <div class="position-relative" [class.rtl-menu-container]="translationService.getCurrentLanguage() === 'ar'">
            <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
              data-kt-menu-trigger="click"
              [attr.data-kt-menu-placement]="translationService.getCurrentLanguage() === 'ar' ? 'bottom-start' : 'bottom-end'"
              [attr.data-kt-menu-flip]="translationService.getCurrentLanguage() === 'ar' ? 'top-start' : 'top-end'">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>

            <div
              class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
              [class.rtl-menu]="translationService.getCurrentLanguage() === 'ar'" data-kt-menu="true">
              <div class="menu-item px-3">
                <div class="menu-content fs-6 fw-bolder px-3 py-4 text-dark-blue"
                  [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
                  [class.rtl-menu-title]="translationService.getCurrentLanguage() === 'ar'">
                  {{ getTranslatedText('ACTIONS') }}
                </div>
              </div>

              <div class="separator mb-3 opacity-75"></div>

              <div class="menu-item px-3">
                <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
                  [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'"
                  [routerLink]="['/requests/render', request.id]">
                  <i class="fa-solid fa-eye" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                  {{ getTranslatedText('VIEW') }}
                </a>
              </div>
              <div class="menu-item px-3">
                <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
                  [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'"
                  [routerLink]="['/requests/assign-to', request.id]">
                  <i class="fa-solid fa-share" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                  {{ getTranslatedText('ASSIGN_TO_BROKERS') }}
                </a>
              </div>
              <div class="menu-item px-3">
                <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
                  [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'" [routerLink]="['/chat']"
                  [queryParams]="{ chatWithUID: request.user.id }">
                  <i class="fa-regular fa-comment-dots" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                  {{ getTranslatedText('CHATS') }}
                </a>
              </div>

              <div class="separator mt-3 opacity-75"></div>

              <div class="menu-item px-3">
                <div class="menu-content px-3 py-3">
                  <a class="btn btn-danger btn-sm px-4 cursor-pointer">
                    <i class="fa-regular fa-eye-slash"></i>
                    {{ getTranslatedText('ARCHIVE') }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
  </table>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ getTranslatedText('LOADING') }}</span>
    </div>
    <p class="mt-3 text-muted">{{ getTranslatedText('LOADING') }}</p>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && rows.length === 0" class="text-center py-5"
    [class.rtl-empty-state]="translationService.getCurrentLanguage() === 'ar'">
    <div class="text-muted fs-4">
      <div class="mb-3">
        <i class="fa-solid fa-file-text fs-1 text-muted"></i>
      </div>
      <p>{{ getTranslatedText('NO_REQUESTS_FOUND') }}</p>
    </div>
  </div>

  <div class="m-2" *ngIf="!loading && rows.length > 0">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>
