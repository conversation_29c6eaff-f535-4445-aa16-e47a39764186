<div class="position-relative" [class.rtl-menu-container]="translationService.getCurrentLanguage() === 'ar'">
  <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
    [attr.data-kt-menu-trigger]="'click'"
    [attr.data-kt-menu-placement]="translationService.getCurrentLanguage() === 'ar' ? 'bottom-start' : 'bottom-end'"
    [attr.data-kt-menu-flip]="translationService.getCurrentLanguage() === 'ar' ? 'top-start' : 'top-end'"
    [attr.id]="'menu-trigger-' + requestId">
    <i class="fa-solid fa-ellipsis-vertical"></i>
  </button>

  <div
    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
    [class.rtl-menu]="translationService.getCurrentLanguage() === 'ar'" [attr.data-kt-menu]="'true'"
    [attr.id]="'menu-' + requestId">
    <div class="menu-item px-3">
      <div class="menu-content fs-6 fw-bolder px-3 py-4 text-dark-blue"
        [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
        [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
        [class.rtl-menu-title]="translationService.getCurrentLanguage() === 'ar'">
        {{ getTranslatedText('ACTIONS') }}
      </div>
    </div>

    <div class="separator mb-3 opacity-75"></div>

    <div class="menu-item px-3">
      <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
        [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'"
        [routerLink]="['/requests/render', requestId]">
        <i class="fa-solid fa-eye" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
        {{ getTranslatedText('VIEW') }}
      </a>
    </div>

    <div class="menu-item px-3">
      <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
        [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'"
        [routerLink]="['/requests/assign-to', requestId]">
        <i class="fa-solid fa-share" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
        {{ getTranslatedText('ASSIGN_TO_BROKERS') }}
      </a>
    </div>
    <div class="separator mt-3 opacity-75"></div>
    <div class="menu-item px-3">
      <div class="menu-content px-3 py-3">
        <a class="btn btn-danger btn-sm px-4 cursor-pointer"
          [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
          <i class="fa-regular fa-eye-slash" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
          {{ getTranslatedText('ARCHIVE') }}
        </a>
      </div>
    </div>
  </div>
</div>
