import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RequestService } from '../../../services/request.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-request-history',
  templateUrl: './request-history.component.html',
  styleUrls: ['./request-history.component.scss']
})

export class RequestHistoryComponent implements OnInit {
  id: string | null = null;
  requestId: number | null = null;
  requestHistory: any[] = [];
  isLoading = false;
  requestUserId :any;

  constructor(
    private route: ActivatedRoute,
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.id = this.route.parent?.snapshot.paramMap.get('id') ?? null;
    this.requestUserId = this.route.snapshot.queryParamMap.get('requestUserId');
    this.requestId = this.id ? Number(this.id) : null;

    if (this.requestId !== null) {
      this.loadRequestHistory();
    } else {
      console.error('No valid request ID found in URL');
      this.isLoading = false;
    }
  }

  loadRequestHistory(): void {
    this.isLoading = true;

    if (this.requestId !== null) {
      this.requestService.getRequestHistory(this.requestId).subscribe({
        next: (response: any) => {
          this.requestHistory = response?.data || [];
          this.isLoading = false;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading request history:', error);
          this.isLoading = false;
          this.cd.detectChanges();
        }
      });
    }
  }

  checkAssign(status: string) {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    if (user?.id != this.requestUserId && user?.role === 'broker' && status == 'Assign') {
      return false;
    } return true;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'CREATE': 'إنشاء',
        'ASSIGN': 'تعيين',
        'UPDATE_STATUS': 'تحديث الحالة',
        'REPLY': 'رد'
      },
      'en': {
        'CREATE': 'Create',
        'ASSIGN': 'Assign',
        'UPDATE_STATUS': 'Update Status',
        'REPLY': 'Reply'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Helper method to get translated status
  getTranslatedStatus(status: string): string {
    const statusKey = status.toUpperCase().replace('_', '_');
    return this.getTranslatedText(statusKey);
  }
}
