<div class="d-flex align-items-center mb-10" [class.rtl-history-item]="translationService.getCurrentLanguage() === 'ar'"
  *ngFor="let history of requestHistory">
  <span *ngIf="checkAssign(history.status)">
    <span *ngIf="history.status === 'Create'" class="bullet bullet-vertical h-40px bg-danger"
      [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
      [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"></span>
    <span *ngIf="history.status === 'Assign'" class="bullet bullet-vertical h-40px bg-primary"
      [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
      [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"></span>
    <span *ngIf="history.status === 'Update_status'" class="bullet bullet-vertical h-40px bg-warning"
      [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
      [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"></span>
    <span *ngIf="history.status === 'Reply'" class="bullet bullet-vertical h-40px bg-success"
      [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
      [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"></span>
  </span>

  <div class="flex-grow-1" [class.rtl-history-content]="translationService.getCurrentLanguage() === 'ar'"
    *ngIf="checkAssign(history.status)">
    <span class="text-gray-800 fw-bolder fs-5"
      [class.rtl-description]="translationService.getCurrentLanguage() === 'ar'">
      {{ history.description }}
    </span>
    <span class="text-muted fw-bold d-block" [class.rtl-date]="translationService.getCurrentLanguage() === 'ar'">
      {{ history.createdAt | date: 'fullDate' }}
    </span>
  </div>

  <span *ngIf="checkAssign(history.status)">
    <span *ngIf="history.status === 'Create'" class="badge badge-light-danger fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Assign'" class="badge badge-light-primary fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Update_status'" class="badge badge-light-warning fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
    <span *ngIf="history.status === 'Reply'" class="badge badge-light-success fs-6 fw-bolder"
      [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedStatus(history.status)
      }}</span>
  </span>
</div>
