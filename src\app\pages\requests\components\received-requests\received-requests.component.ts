import { ActivatedRoute } from '@angular/router';
import { RequestService } from './../../services/request.service';
import { ChangeDetectorRef, Component, OnInit, AfterViewInit } from '@angular/core';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import Swal from 'sweetalert2';
import { environment } from 'src/environments/environment';
import { AuthenticationService } from 'src/app/pages/authentication';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-received-requests',
  templateUrl: './received-requests.component.html',
  styleUrls: ['./received-requests.component.scss'],
})

export class ReceivedRequestsComponent extends BaseGridComponent implements OnInit, AfterViewInit {

  user: any;
  brokerId :any;
  newRequestsCount = 0;
  requests: Request[] = [];

  constructor(protected cd: ChangeDetectorRef, protected requestService: RequestService, private route: ActivatedRoute, private authenticationService: AuthenticationService, public translationService: TranslationService) {
    super(cd);
    this.setService(requestService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.page.filters = { brokerId: this.user?.brokerId };
  }

  ngOnInit() {
    this.user = this.authenticationService.getSessionUser();
    this.brokerId = this.user?.brokerId;
    this.page.pageNumber = 0;
    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
  }

  async ngAfterViewInit(): Promise<void> {
    this.requestService.getFilters().subscribe(async (filters: any) => {
      this.page.filters = { brokerId: this.brokerId, ...(filters || {}) };
      await this.reloadTable(this.page);
    });
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;

    try {
      // Clone filters to avoid modifying the original
      const requestFilters = { ...this.page.filters };

      // Remove null/empty filters before sending to backend
      Object.keys(requestFilters).forEach(key => {
        if (requestFilters[key] === null || requestFilters[key] === '') {
          delete requestFilters[key];
        }
      });

      const pagedData = await this._service.getAll({
        ...this.page,
        filters: requestFilters
      }).toPromise();

      this.processResponse(pagedData);
    } catch (error) {
      this.handleError(error);
    }
  }

  private processResponse(pagedData: any) {
    this.rows = Array.isArray(pagedData?.data?.data) ? pagedData.data.data : [];
    this.rows = [...this.rows];
    this.page.totalElements = pagedData?.data.count || 0;
    this.page.count = Math.ceil(this.page.totalElements / this.page.size);
    this.newRequestsCount = pagedData?.data?.newRequestsCount || 0;
    this.requestService.setNewRequestsCount(this.newRequestsCount);

    this.cd.markForCheck();
    this.loading = false;
    this.afterGridLoaded();

    setTimeout(() => {
      MenuComponent.reinitialization();
    }, 0);
    // MenuComponent.reinitialization();
  }

  private handleError(error: any) {
    console.error('API Error:', error);
    this.cd.markForCheck();
    this.loading = false;
    Swal.fire(error.error.message, '', 'error');
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'REQUEST_USER': 'مستخدم الطلب',
        'REQUEST_OWNER': 'مالك الطلب',
        'REQUEST_TITLE': 'عنوان الطلب',
        'REQUEST_DATE': 'تاريخ الطلب',
        'SPECIALIZATION_SCOPE': 'نطاق التخصص',
        'REQUEST_TYPE': 'نوع الطلب',
        'STATUS': 'الحالة',
        'ACTIONS': 'الإجراءات',
        'NEW': 'جديد',
        'IN_PROCESSING': 'قيد المعالجة',
        'FINISHED': 'مكتمل',
        'PURCHASING': 'شراء',
        'SELL': 'بيع',
        'RENT_IN': 'استئجار',
        'RENT_OUT': 'تأجير',
        'CLIENT': 'عميل',
        'BROKER': 'وسيط',
        'DEVELOPER': 'مطور',
        'ADMIN': 'مدير',
        'VIEW': 'عرض',
        'ASSIGN_TO_BROKERS': 'تعيين للوسطاء',
        'CHATS': 'المحادثات',
        'ARCHIVE': 'أرشفة',
        'LOADING': 'جاري التحميل...',
        'NO_REQUESTS_FOUND': 'لا توجد طلبات'
      },
      'en': {
        'REQUEST_USER': 'Request User',
        'REQUEST_OWNER': 'Request Owner',
        'REQUEST_TITLE': 'Request Title',
        'REQUEST_DATE': 'Request Date',
        'SPECIALIZATION_SCOPE': 'Specialization Scope',
        'REQUEST_TYPE': 'Request Type',
        'STATUS': 'Status',
        'ACTIONS': 'Actions',
        'NEW': 'New',
        'IN_PROCESSING': 'In Processing',
        'FINISHED': 'Finished',
        'PURCHASING': 'Purchasing',
        'SELL': 'Sell',
        'RENT_IN': 'Rent In',
        'RENT_OUT': 'Rent Out',
        'CLIENT': 'Client',
        'BROKER': 'Broker',
        'DEVELOPER': 'Developer',
        'ADMIN': 'Admin',
        'VIEW': 'View',
        'ASSIGN_TO_BROKERS': 'Assign to broker(s)',
        'CHATS': 'Chats',
        'ARCHIVE': 'Archive',
        'LOADING': 'Loading...',
        'NO_REQUESTS_FOUND': 'No requests found'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
