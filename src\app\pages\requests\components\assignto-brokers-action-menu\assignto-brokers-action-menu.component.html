<div [class.rtl-menu]="translationService.getCurrentLanguage() === 'ar'">
  <div class="menu-item px-3">
    <div class="menu-content fs-6 fw-bolder px-3 py-4 text-dark-blue"
      [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
      [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
      [class.rtl-menu-title]="translationService.getCurrentLanguage() === 'ar'">
      {{ getTranslatedText('ACTIONS') }}
    </div>
  </div>

  <div class="separator mb-3 opacity-75"></div>

  <div class="menu-item px-3">
    <a class="menu-link px-3 cursor-pointer text-hover-dark-blue"
      [class.rtl-menu-link]="translationService.getCurrentLanguage() === 'ar'" [routerLink]="['/requests/render']">
      <i class="fa-solid fa-eye" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
        [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
      {{ getTranslatedText('VIEW') }}
    </a>
  </div>

  <div class="separator mt-3 opacity-75"></div>

  <div class="menu-item px-3">
    <div class="menu-content px-3 py-3">
      <a class="btn btn-danger btn-sm px-4 cursor-pointer"
        [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
        <i class="fa-solid fa-trash" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
        {{ getTranslatedText('DELETE') }}
      </a>
    </div>
  </div>
</div>
