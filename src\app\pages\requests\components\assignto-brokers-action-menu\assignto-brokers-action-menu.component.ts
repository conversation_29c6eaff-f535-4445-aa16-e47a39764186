import { Component, HostBinding, OnInit } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-assignto-brokers-action-menu',
  // standalone: true,
  // imports: [],
  templateUrl: './assignto-brokers-action-menu.component.html',
  styleUrl: './assignto-brokers-action-menu.component.scss',
})
export class AssigntoBrokersActionMenuComponent implements OnInit {
  @HostBinding('class') class =
    'menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px';
  @HostBinding('attr.data-kt-menu') dataKtMenu = 'true';

  constructor(public translationService: TranslationService) {}

  ngOnInit(): void {}

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'ACTIONS': 'الإجراءات',
        'VIEW': 'عرض',
        'DELETE': 'حذف'
      },
      'en': {
        'ACTIONS': 'Actions',
        'VIEW': 'View',
        'DELETE': 'Delete'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
