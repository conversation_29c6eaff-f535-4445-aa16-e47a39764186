.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}

.position-relative {
  position: relative;

  .position-absolute {
    position: absolute;
  }
}

// RTL Support for Request Overview
.rtl-layout {
  direction: rtl;
  text-align: right;

  .rtl-section-header {
    text-align: right;
    font-family: 'Noto Ku<PERSON> Arabic', sans-serif;

    i {
      margin-right: 0;
      margin-left: 0.5rem;
    }
  }

  .rtl-content {
    direction: rtl;
    text-align: right;

    .rtl-field {
      text-align: right;

      .rtl-label {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: right;
      }

      .rtl-value {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: right;
      }
    }
  }

  // Badge styles for RTL
  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
    direction: rtl;
  }

  // Modal RTL support
  .modal {
    direction: rtl;

    .modal-header {
      text-align: right;

      .modal-title {
        font-family: 'Noto <PERSON>', sans-serif;
      }

      .btn-close {
        margin-left: auto;
        margin-right: 0;
      }
    }

    .modal-body {
      text-align: center;
    }

    .modal-footer {
      justify-content: flex-start;

      .btn {
        font-family: 'Hacen Liner Screen St', sans-serif;
      }
    }
  }

  // Media gallery RTL
  .d-flex {
    &.align-items-center {
      flex-direction: row-reverse;

      span {
        text-align: right;
      }
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  h6 {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  span {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}
