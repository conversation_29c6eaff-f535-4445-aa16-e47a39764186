// RTL Support for Filter Dropdown
.rtl-dropdown {
  direction: rtl;
  text-align: right;

  .rtl-button {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: center;
  }

  .rtl-menu {
    direction: rtl;
    text-align: right;

    .rtl-card {
      direction: rtl;
      text-align: right;

      .rtl-header {
        text-align: right;

        .rtl-title {
          font-family: 'Noto Kufi Arabic', sans-serif;
          font-weight: bold;
          text-align: right;
        }
      }

      .rtl-body {
        direction: rtl;
        text-align: right;

        .rtl-form {
          direction: rtl;

          .rtl-field {
            text-align: right;

            .rtl-label {
              font-family: 'Hacen Liner Screen St', sans-serif;
              text-align: right;
              display: block;
              margin-bottom: 0.5rem;
            }

            .rtl-select {
              direction: rtl;
              text-align: right;
              font-family: 'Hacen Liner Screen St', sans-serif;

              option {
                direction: rtl;
                text-align: right;
                font-family: 'Hacen Liner Screen St', sans-serif;
              }
            }
          }

          .rtl-actions {
            flex-direction: row-reverse;

            .rtl-button {
              font-family: 'Hacen Liner Screen St', sans-serif;
            }
          }
        }
      }
    }
  }
}

.dropdown-menu-left {
  right: auto !important;
  left: 0 !important;
  transform: translateX(-100%) !important;
}

.dropdown-menu-right {
  left: auto !important;
  right: 0 !important;
  transform: translateX(100%) !important;
}

// Arabic font support
:host-context(html[lang="ar"]) {
  .dropdown {
    direction: rtl;

    .btn {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .card-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
    }

    .form-label {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .form-select {
      font-family: 'Hacen Liner Screen St', sans-serif;
      direction: rtl;
      text-align: right;

      option {
        font-family: 'Hacen Liner Screen St', sans-serif;
        direction: rtl;
        text-align: right;
      }
    }
  }
}
