import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RequestService } from '../../services/request.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-assign-to-brokers',
  templateUrl: './assign-to-brokers.component.html',
  styleUrl: './assign-to-brokers.component.scss',
})
export class AssignToBrokersComponent implements OnInit {
  requestId: number;
  AssignBroker: any[] = [];
  isLoading = false;

  trackById(index: number, broker: any): number {
    return broker.id;
  }

  selectAll = false;

  selectAllBrokers() {
    this.AssignBroker.forEach((broker) => {
      broker.selected = this.selectAll;
    });
    this.printSelectedIds();
  }

  updateSelectAllStatus() {
    this.selectAll = this.AssignBroker.every((broker) => broker.selected);
    this.printSelectedIds();
  }

  printSelectedIds() {
    const selectedIds = this.AssignBroker.filter(
      (broker) => broker.selected
    ).map((broker) => broker.id);

    console.log('Selected IDs:', selectedIds);
  }

  get selectedCount(): number {
    return this.AssignBroker.filter((broker) => broker.selected).length;
  }

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    // Get requestId from route parameters
    this.route.paramMap.subscribe((params) => {
      this.requestId = Number(params.get('id'));
      if (this.requestId) {
        this.loadRequestHistory();
      } else {
        console.error('No requestId provided in route parameters');
      }
    });
  }

  loadRequestHistory(): void {
    this.isLoading = true;

    this.requestService.getRecommendedBrokers(this.requestId).subscribe({
      next: (response: any) => {
        this.AssignBroker = response?.data || [];
        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading broker history:', error);
        this.isLoading = false;
      },
    });
  }

  assignSelectedBrokers(): void {
    const selectedBrokerIds = this.AssignBroker.filter(
      (broker) => broker.selected
    ).map((broker) => broker.id);

    this.requestService
      .assignBrokersToRequest(this.requestId, selectedBrokerIds)
      .subscribe({
        next: (res) => {
          console.log('Assigned successfully:', res);
        },
        error: (err) => {
          console.error('Assignment error:', err);
        },
      });
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'REQUESTS': 'الطلبات',
        'SEARCH': 'بحث...',
        'FILTER': 'تصفية',
        'SORT': 'ترتيب',
        'BROKER_NAME': 'اسم الوسيط',
        'ACCOUNT_TYPE': 'نوع الحساب',
        'TYPE': 'النوع',
        'AREA': 'المنطقة',
        'SPECIALIZATION': 'التخصص',
        'ACTIONS': 'الإجراءات',
        'ASSIGN_TO_BROKER': 'تعيين للوسيط',
        'GOLDEN_ACCOUNT': 'حساب ذهبي',
        'SILVER_ACCOUNT': 'حساب فضي',
        'BRONZE_ACCOUNT': 'حساب برونزي',
        'FREE': 'مجاني',
        'REAL_ESTATE_BROKAGE_COMPANY': 'شركة وساطة عقارية',
        'INDEPENDENT': 'مستقل'
      },
      'en': {
        'REQUESTS': 'Requests',
        'SEARCH': 'Search...',
        'FILTER': 'Filter',
        'SORT': 'Sort',
        'BROKER_NAME': 'Broker name',
        'ACCOUNT_TYPE': 'Account type',
        'TYPE': 'Type',
        'AREA': 'Area',
        'SPECIALIZATION': 'Specialization',
        'ACTIONS': 'Actions',
        'ASSIGN_TO_BROKER': 'Assign to broker',
        'GOLDEN_ACCOUNT': 'Golden Account',
        'SILVER_ACCOUNT': 'Silver Account',
        'BRONZE_ACCOUNT': 'Bronze Account',
        'FREE': 'Free',
        'REAL_ESTATE_BROKAGE_COMPANY': 'Real Estate Brokage Company',
        'INDEPENDENT': 'Independent'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
