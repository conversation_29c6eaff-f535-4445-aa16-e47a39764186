<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-5 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2"
          [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
          <div class="d-flex flex-column my-4">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-800 fs-2 fw-bolder" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-title]="translationService.getCurrentLanguage() === 'ar'">{{ request?.title ||
                getTranslatedText('LOADING') }}</span>
            </div>

            <div class="d-flex flex-wrap fw-bold fs-5 mb-4"
              [class.pe-2]="translationService.getCurrentLanguage() !== 'ar'"
              [class.ps-2]="translationService.getCurrentLanguage() === 'ar'"
              [class.rtl-info]="translationService.getCurrentLanguage() === 'ar'">
              <span class="d-flex align-items-center text-gray-600 mb-2"
                [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-info-item]="translationService.getCurrentLanguage() === 'ar'">
                <i class="fa-solid fa-user text-mid-blue"
                  [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                {{ request?.user?.name || getTranslatedText('N_A') }}
              </span>
              <span class="d-flex align-items-center text-gray-600 mb-2"
                [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-info-item]="translationService.getCurrentLanguage() === 'ar'">
                <i class="fa-solid fa-calendar text-mid-blue"
                  [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                {{ request?.createdAt || getTranslatedText('N_A') }}
              </span>
              <span class="d-flex align-items-center text-gray-600 mb-2"
                [class.me-5]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-5]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-info-item]="translationService.getCurrentLanguage() === 'ar'">
                <i class="fa-solid fa-phone text-mid-blue"
                  [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                {{ request?.user?.phone || getTranslatedText('N_A') }}
              </span>
            </div>
          </div>

          <div class="d-flex my-4" [class.rtl-actions]="translationService.getCurrentLanguage() === 'ar'">
            <span class="badge badge-light-dark-blue px-3 py-3 fw-bold fs-6"
              [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
              [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
              [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedStatus(request?.status) }}
            </span>
            <!--broker actions-->
            <div *ngIf="user?.role == 'broker'">
              <div *ngIf="request?.status == 'new'">
                <a class="btn btn-sm btn-mid-blue cursor-pointer fw-bold fs-6"
                  [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
                  [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'"
                  (click)="updateRequestStatus(request.id, userId, 'in_processing')">
                  <i class="fa-solid fa-play" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                  {{ getTranslatedText('START_PROCESSING') }}
                </a>
              </div>
            </div>
            <div *ngIf="user?.role == 'broker'">
              <a class="btn btn-sm btn-danger cursor-pointer fw-bold fs-6"
                [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'"
                (click)="archiveRequest(request.id, brokerId)">
                <i class="fa-regular fa-eye-slash" [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                {{ getTranslatedText('ARCHIVE') }}
              </a>
            </div>
            <!--client actions-->
            <!-- <div *ngIf="user?.role == 'client'">
              <div *ngIf="request?.status != 'finished'">
                <a class="btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6" (click)="updateRequestStatus(request.id, userId, 'finished')">
                  <i class="fa-solid fa-close"></i>
                  Finish Request
                </a>
              </div>
            </div> -->
          </div>
        </div>

        <div class="d-flex flex-wrap flex-stack">
          <div class="d-flex flex-column flex-grow-1" [class.pe-8]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ps-8]="translationService.getCurrentLanguage() === 'ar'">
            <div class="d-flex flex-wrap">
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 mb-3"
                [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-stats]="translationService.getCurrentLanguage() === 'ar'">
                <div class="d-flex align-items-center">
                  <div class="fs-2 fw-bolder">
                    <i class="fa-solid fa-reply text-mid-blue"
                      [class.me-1]="translationService.getCurrentLanguage() !== 'ar'"
                      [class.ms-1]="translationService.getCurrentLanguage() === 'ar'"></i>
                    {{ request?.numberOfReplies || 0 }}
                  </div>
                </div>
                <div class="fw-bold fs-6 text-gray-500"
                  [class.rtl-text]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('REPLIES') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="d-flex overflow-auto mb-5" [class.rtl-tabs]="translationService.getCurrentLanguage() === 'ar'">
      <ul class="nav nav-stretch nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap"
        [class.rtl-nav]="translationService.getCurrentLanguage() === 'ar'">
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="overview"
            routerLinkActive="active">
            {{ getTranslatedText('OVERVIEW') }}
          </a>
        </li>
        <li class="nav-item" *ngIf="canReply">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="units-recommendation"
            routerLinkActive="active">
            {{ getTranslatedText('UNITS_RECOMMENDATION') }}
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" [routerLink]="['history']"
            [queryParams]="{ requestUserId: request?.user?.id }" routerLinkActive="active">
            {{ getTranslatedText('HISTORY') }}
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link btn btn-active-dark-blue btn-light-primary"
            [class.me-6]="translationService.getCurrentLanguage() !== 'ar'"
            [class.ms-6]="translationService.getCurrentLanguage() === 'ar'"
            [class.rtl-tab-link]="translationService.getCurrentLanguage() === 'ar'" routerLink="replies"
            routerLinkActive="active">
            {{ getTranslatedText('REPLIES') }}
          </a>
        </li>
      </ul>
    </div>

    <div class="card-body pt-3 pb-0 px-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
