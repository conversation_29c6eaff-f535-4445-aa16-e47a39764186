import { Component, Input, Output, EventEmitter, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-filter-drop-down',
  templateUrl: './filter-drop-down.component.html',
  styleUrls: ['./filter-drop-down.component.scss']
})

export class FilterDropDownComponent implements OnInit {

  @Input() placeholder: string = 'Select an option';
  @Output() filtersApplied = new EventEmitter<any>();

  isOpen: boolean = false;
  selectedMainOption: string = '';
  selectedChildOption: string = '';
  filteredOptions: string[] = [];
  activeChildDropdown: string | null = null;
  form: FormGroup;
  options = {
    specializationScope: [
      'purchase_sell_outside_compound',
      // 'purchase_sell_inside_compound',
      'primary_inside_compound',
      'resale_inside_compound',
      'rentals_outside_compound',
      'rentals_inside_compound'
    ],
    status: ['new', 'in_processing', 'finished'],
    type: ['sell', 'purchasing', 'rent_in', 'rent_out'],
  };

  constructor(private fb: FormBuilder, public translationService: TranslationService) {
    this.form = this.fb.group({
      specializationScope: [''],
      status: [''],
      type: ['']
    });
  }

  ngOnInit() {}

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isOpen = !this.isOpen;
    if (!this.isOpen) {
      this.activeChildDropdown = null;
    }
  }

  onSubmit() {
    if (this.form.valid) {
      // Emit the form values to the parent component
      this.filtersApplied.emit(this.form.value);
      this.closeDropdown();
    }
  }

  closeDropdown() {
    this.isOpen = false;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'FILTERS': 'فلتر',
        'SPECIALIZATION_SCOPE': 'نطاق التخصص',
        'STATUS': 'الحالة',
        'TYPE': 'النوع',
        'SELECT': 'اختر...',
        'APPLY': 'تطبيق',
        'RESET': 'إعادة تعيين',
        'LOADING': 'جاري التحميل',
        // Specialization Scope Options
        'PURCHASE_SELL_OUTSIDE_COMPOUND': 'شراء/بيع خارج الكمبوند',
        'PRIMARY_INSIDE_COMPOUND': 'أولي داخل الكمبوند',
        'RESALE_INSIDE_COMPOUND': 'إعادة بيع داخل الكمبوند',
        'RENTALS_OUTSIDE_COMPOUND': 'إيجارات خارج الكمبوند',
        'RENTALS_INSIDE_COMPOUND': 'إيجارات داخل الكمبوند',
        // Status Options
        'NEW': 'جديد',
        'IN_PROCESSING': 'قيد المعالجة',
        'FINISHED': 'مكتمل',
        // Type Options
        'SELL': 'بيع',
        'PURCHASING': 'شراء',
        'RENT_IN': 'استئجار',
        'RENT_OUT': 'تأجير'
      },
      'en': {
        'FILTERS': 'Filters',
        'SPECIALIZATION_SCOPE': 'Specialization Scope',
        'STATUS': 'Status',
        'TYPE': 'Type',
        'SELECT': 'Select...',
        'APPLY': 'Apply',
        'RESET': 'Reset',
        'LOADING': 'Loading',
        // Specialization Scope Options
        'PURCHASE_SELL_OUTSIDE_COMPOUND': 'Purchase/Sell Outside Compound',
        'PRIMARY_INSIDE_COMPOUND': 'Primary Inside Compound',
        'RESALE_INSIDE_COMPOUND': 'Resale Inside Compound',
        'RENTALS_OUTSIDE_COMPOUND': 'Rentals Outside Compound',
        'RENTALS_INSIDE_COMPOUND': 'Rentals Inside Compound',
        // Status Options
        'NEW': 'New',
        'IN_PROCESSING': 'In Processing',
        'FINISHED': 'Finished',
        // Type Options
        'SELL': 'Sell',
        'PURCHASING': 'Purchasing',
        'RENT_IN': 'Rent In',
        'RENT_OUT': 'Rent Out'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Helper method to get translated option value
  getTranslatedOption(option: string, category: string): string {
    const optionKey = option.toUpperCase().replace(/_/g, '_');
    return this.getTranslatedText(optionKey);
  }
}
