// RTL Support for Received Requests Actions Menu
.rtl-menu-container {
  direction: rtl;

  .rtl-menu {
    direction: rtl;
    text-align: right;

    .rtl-menu-title {
      text-align: right;
      font-family: 'Noto <PERSON> Arabic', sans-serif;
    }

    .rtl-menu-link {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
      direction: rtl;

      i {
        margin-right: 0;
        margin-left: 0.25rem;
      }
    }

    .rtl-button {
      font-family: 'Hacen Liner Screen St', sans-serif;
      direction: rtl;

      i {
        margin-right: 0;
        margin-left: 0.25rem;
      }
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  .menu {
    direction: rtl;

    .menu-content {
      font-family: 'Noto Kufi Arabic', sans-serif;
      text-align: right;
    }

    .menu-link {
      font-family: 'Hacen Liner Screen St', sans-serif;
      text-align: right;
      direction: rtl;
    }

    .btn {
      font-family: 'Hacen Liner Screen St', sans-serif;
      direction: rtl;
    }
  }
}