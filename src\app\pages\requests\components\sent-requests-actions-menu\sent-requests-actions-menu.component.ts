import { Component, Input, AfterViewInit } from '@angular/core';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-sent-requests-actions-menu',
  templateUrl: './sent-requests-actions-menu.component.html',
  styleUrls: ['./sent-requests-actions-menu.component.scss']
})
export class SentRequestsActionsMenuComponent implements AfterViewInit {
  @Input() requestId: number;

  constructor(private translationService: TranslationService) {}

  ngAfterViewInit() {
    // Initialize menu after view is ready
    setTimeout(() => {
      MenuComponent.reinitialization();
    }, 100);
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'ACTIONS': 'الإجراءات',
        'VIEW': 'عرض',
        'ASSIGN_TO_BROKERS': 'تعيين للوسطاء',
        'ARCHIVE': 'أرشفة'
      },
      'en': {
        'ACTIONS': 'Actions',
        'VIEW': 'View',
        'ASSIGN_TO_BROKERS': 'Assign to broker(s)',
        'ARCHIVE': 'Archive'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
