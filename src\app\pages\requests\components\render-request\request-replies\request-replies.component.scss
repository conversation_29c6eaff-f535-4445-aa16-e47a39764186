// RTL Support for Request Replies
.rtl-layout {
  direction: rtl;
  text-align: right;

  .table-responsive {
    direction: rtl;

    .table {
      direction: rtl;

      .rtl-table-header {
        direction: rtl;
        text-align: right;

        .rtl-th {
          text-align: right;
          font-family: 'Noto <PERSON> Arabic', sans-serif;

          span {
            font-family: 'Hacen Liner Screen St', sans-serif;
          }
        }
      }

      tbody {
        direction: rtl;

        tr {
          direction: rtl;

          td {
            text-align: right;
            font-family: 'Hacen Liner Screen St', sans-serif;

            .badge {
              font-family: 'Hacen Liner Screen St', sans-serif;
            }

            .btn {
              font-family: 'Hacen Liner Screen St', sans-serif;
            }
          }
        }
      }
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  .table {
    th {
      font-family: 'Noto Ku<PERSON> Arabic', sans-serif;
    }

    td {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .badge {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .btn {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }
}
