import {
  Component,
  OnInit,
  HostBinding,
  Input,
  AfterViewInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { ContractService } from '../../../../services/contract.service';
import { TranslationService } from 'src/app/modules/i18n';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-contract-requests-dropdown-action-menu',
  templateUrl: './contract-requests-dropdown-action-menu.component.html',
  styleUrl: './contract-requests-dropdown-action-menu.component.scss',
})
export class ContractRequestsDropdownActionMenuComponent {
  @HostBinding('class') class = 'dropdown-menu';

  @Input() id: number | any; // This will be the request ID
  @Output() actionCompleted = new EventEmitter<void>();

  constructor(
    private contractService: ContractService,
    public translationService: TranslationService
  ) {}

  handleRequestAction(action: 'accepted' | 'declined') {
    console.log('Contract Request ID received:', this.id);
    console.log('Type of ID:', typeof this.id);

    if (!this.id || this.id === undefined || this.id === null) {
      Swal.fire(
        this.getTranslatedText('ERROR'),
        this.getTranslatedText('REQUEST_ID_MISSING'),
        'error'
      );
      return;
    }

    const isApprove = action === 'accepted';

    Swal.fire({
      title: isApprove ? this.getTranslatedText('ACCEPT_REQUEST') : this.getTranslatedText('DECLINE_REQUEST'),
      text: isApprove ? this.getTranslatedText('ARE_YOU_SURE_ACCEPT') : this.getTranslatedText('ARE_YOU_SURE_DECLINE'),
      icon: isApprove ? 'question' : 'warning',
      showCancelButton: true,
      confirmButtonColor: isApprove ? '#28a745' : '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: isApprove ? this.getTranslatedText('YES_ACCEPT') : this.getTranslatedText('YES_DECLINE'),
      cancelButtonText: this.getTranslatedText('CANCEL'),
    }).then((result) => {
      if (result.isConfirmed) {
        const requestObservable = isApprove
          ? this.contractService.approveRequest(this.id)
          : this.contractService.rejectRequest(this.id);

        requestObservable.subscribe({
          next: () => {
            Swal.fire(
              isApprove ? this.getTranslatedText('ACCEPTED') : this.getTranslatedText('DECLINED'),
              isApprove ? this.getTranslatedText('REQUEST_ACCEPTED') : this.getTranslatedText('REQUEST_DECLINED'),
              'success'
            );
            this.actionCompleted.emit();
          },
          error: (error) => {
            console.error(`Error ${action}ing request:`, error);
            Swal.fire(
              this.getTranslatedText('ERROR'),
              isApprove ? this.getTranslatedText('FAILED_TO_ACCEPT') : this.getTranslatedText('FAILED_TO_DECLINE'),
              'error'
            );
          },
        });
      }
    });
  }

  onApprove() {
    this.handleRequestAction('accepted');
  }

  onReject() {
    this.handleRequestAction('declined');
  }

  getCurrentLanguage(): string {
    return this.translationService.getCurrentLanguage();
  }

  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'ACTIONS': 'الإجراءات',
        'REQUEST_ID': 'رقم الطلب',
        'ACCEPT': 'قبول',
        'DECLINE': 'رفض',
        'ACCEPT_REQUEST': 'قبول الطلب',
        'DECLINE_REQUEST': 'رفض الطلب',
        'ARE_YOU_SURE_ACCEPT': 'هل أنت متأكد من قبول طلب العقد هذا؟',
        'ARE_YOU_SURE_DECLINE': 'هل أنت متأكد من رفض طلب العقد هذا؟',
        'YES_ACCEPT': 'نعم، قبول',
        'YES_DECLINE': 'نعم، رفض',
        'CANCEL': 'إلغاء',
        'ACCEPTED': 'تم القبول!',
        'DECLINED': 'تم الرفض!',
        'REQUEST_ACCEPTED': 'تم قبول طلب العقد.',
        'REQUEST_DECLINED': 'تم رفض طلب العقد.',
        'ERROR': 'خطأ',
        'FAILED_TO_ACCEPT': 'فشل في قبول الطلب. يرجى المحاولة مرة أخرى.',
        'FAILED_TO_DECLINE': 'فشل في رفض الطلب. يرجى المحاولة مرة أخرى.',
        'REQUEST_ID_MISSING': 'رقم طلب العقد مفقود. يرجى تحديث الصفحة والمحاولة مرة أخرى.'
      },
      'en': {
        'ACTIONS': 'Actions',
        'REQUEST_ID': 'Request ID',
        'ACCEPT': 'Accept',
        'DECLINE': 'Decline',
        'ACCEPT_REQUEST': 'Accept Request',
        'DECLINE_REQUEST': 'Decline Request',
        'ARE_YOU_SURE_ACCEPT': 'Are you sure you want to accept this contract request?',
        'ARE_YOU_SURE_DECLINE': 'Are you sure you want to decline this contract request?',
        'YES_ACCEPT': 'Yes, accept',
        'YES_DECLINE': 'Yes, decline',
        'CANCEL': 'Cancel',
        'ACCEPTED': 'Accepted!',
        'DECLINED': 'Declined!',
        'REQUEST_ACCEPTED': 'Contract request has been accepted.',
        'REQUEST_DECLINED': 'Contract request has been declined.',
        'ERROR': 'Error',
        'FAILED_TO_ACCEPT': 'Failed to accept request. Please try again.',
        'FAILED_TO_DECLINE': 'Failed to decline request. Please try again.',
        'REQUEST_ID_MISSING': 'Contract request ID is missing. Please refresh the page and try again.'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
