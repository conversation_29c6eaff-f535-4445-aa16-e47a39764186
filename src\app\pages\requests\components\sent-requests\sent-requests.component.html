<div class="table-responsive">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <th class="w-25px ps-4 rounded-start">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
              data-kt-check-target=".widget-13-check" />
          </div>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('title')">
          {{ getTranslatedText('REQUEST_TITLE') }}
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('title') }}</span>
        </th>
        <th class="min-w-140px cursor-pointer" (click)="sortData('created_at')">
          {{ getTranslatedText('REQUEST_DATE') }}
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('created_at') }}</span>
        </th>
        <th class="min-w-120px cursor-pointer" (click)="sortData('specialization_scope')">
          {{ getTranslatedText('SPECIALIZATION_SCOPE') }}
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('specialization_scope') }}</span>
        </th>
        <th class="min-w-120px cursor-pointer" (click)="sortData('type')">
          {{ getTranslatedText('REQUEST_TYPE') }}
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('type') }}</span>
        </th>
        <th class="min-w-120px cursor-pointer" (click)="sortData('status')">
          {{ getTranslatedText('STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
        </th>
        <th class="min-w-100px text-end rounded-end pe-4">{{ getTranslatedText('ACTIONS') }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let request of rows">
        <td class="ps-4">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input widget-13-check" type="checkbox" value="1" />
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="d-flex justify-content-start flex-column">
              <a [routerLink]="['/requests/render', request.id]"
                class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                {{ request.title }}
              </a>
            </div>
          </div>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ request.createdAt }}
          </span>
        </td>
        <td>
          <span class="badge badge-dark-blue fs-6">
            {{ request.specializationScope }}
          </span>
        </td>
        <td>
          <span class="badge badge-light-info fs-6">
            {{ request.type }}
          </span>
        </td>
        <td>
          <span *ngIf="request.status === 'new'" class="badge badge-light-danger fs-6">
            {{ getTranslatedText('NEW') }}
          </span>
          <span *ngIf="request.status === 'in_processing'" class="badge badge-light-warning fs-6">
            {{ getTranslatedText('IN_PROCESSING') }}
          </span>
          <span *ngIf="request.status === 'finished'" class="badge badge-light-warning fs-6">
            {{ getTranslatedText('FINISHED') }}
          </span>
        </td>
        <td class="text-end pe-4">
          <app-sent-requests-actions-menu [requestId]="request.id"></app-sent-requests-actions-menu>
        </td>
      </tr>
    </tbody>
  </table>

  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>
