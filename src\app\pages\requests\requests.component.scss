// RTL Support for Requests Page
.rtl-layout {
  direction: rtl;
  text-align: right;

  .rtl-header {
    flex-direction: row-reverse;

    .rtl-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      font-weight: bold;
      margin-right: 0;
      margin-left: 0.25rem;
    }
  }

  .rtl-search {
    .rtl-input {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;

      &::placeholder {
        text-align: right;
        font-family: 'Hacen Liner Screen St', sans-serif;
      }
    }
  }

  .rtl-tabs {
    .rtl-nav {
      flex-direction: row-reverse;

      .rtl-tab-link {
        font-family: 'Hacen Liner Screen St', sans-serif;
        text-align: center;

        .badge {
          font-family: 'Hacen Liner Screen St', sans-serif;
        }
      }
    }
  }

  // General RTL styles
  .card-body {
    text-align: right;
  }

  .d-flex {
    &.justify-content-between {
      flex-direction: row-reverse;
    }
  }

  // Filter dropdown RTL support
  app-filter-drop-down {
    direction: rtl;
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .nav-link {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}
