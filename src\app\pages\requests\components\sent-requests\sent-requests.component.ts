import { RequestService } from './../../services/request.service';
import { ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, AfterViewInit } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import Swal from 'sweetalert2';
import { environment } from 'src/environments/environment';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { AuthenticationService } from 'src/app/pages/authentication';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-sent-requests',
  templateUrl: './sent-requests.component.html',
  styleUrl: './sent-requests.component.scss',
})

export class SentRequestsComponent extends BaseGridComponent implements AfterViewInit {

  @Input() activeFilters: any = {};
  @Output() newRequestsCountChanged = new EventEmitter<number>();
  user : any;
  userId :any;

  constructor(protected cd: ChangeDetectorRef, protected requestService: RequestService, private authenticationService:AuthenticationService, private translationService: TranslationService) {
    super(cd);
    this.setService(requestService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.user = this.authenticationService.getSessionUser();
    this.userId = this.user.id;
    this.page.filters = {userId : this.userId};
  }

  ngOnInit() {
    this.page.pageNumber = 0;
    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
  }

  async ngAfterViewInit(): Promise<void> {
    this.requestService.getFilters().subscribe(async (filters: any) => {
      this.page.filters = { userId: this.userId, ...(filters || {}) };
      await this.reloadTable(this.page);
    });
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;

    try {
      // Clone filters to avoid modifying the original
      const requestFilters = { ...this.page.filters };

      // Remove null/empty filters before sending to backend
      Object.keys(requestFilters).forEach(key => {
        if (requestFilters[key] === null || requestFilters[key] === '') {
          delete requestFilters[key];
        }
      });

      const pagedData = await this._service.getAll({
        ...this.page,
        filters: requestFilters
      }).toPromise();

      this.processResponse(pagedData);
    } catch (error) {
      this.handleError(error);
    }
  }

  private processResponse(pagedData: any) {
    this.rows = Array.isArray(pagedData?.data?.data) ? pagedData.data.data : [];
    this.rows = [...this.rows];
    this.page.totalElements = pagedData?.data.count || 0;
    this.page.count = Math.ceil(this.page.totalElements / this.page.size);

    this.cd.markForCheck();
    this.loading = false;
    this.afterGridLoaded();
    MenuComponent.reinitialization();
  }

  private handleError(error: any) {
    console.error('API Error:', error);
    console.error('API Error:', error.error.message);
    this.cd.markForCheck();
    this.loading = false;
    Swal.fire(error.error.message, '', 'error');
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'REQUEST_TITLE': 'عنوان الطلب',
        'REQUEST_DATE': 'تاريخ الطلب',
        'SPECIALIZATION_SCOPE': 'نطاق التخصص',
        'REQUEST_TYPE': 'نوع الطلب',
        'STATUS': 'الحالة',
        'ACTIONS': 'الإجراءات',
        'NEW': 'جديد',
        'IN_PROCESSING': 'قيد المعالجة',
        'FINISHED': 'مكتمل',
        'PURCHASING': 'شراء',
        'SELL': 'بيع',
        'RENT_IN': 'إيجار داخلي',
        'RENT_OUT': 'إيجار خارجي',
        'LOADING': 'جاري التحميل...',
        'NO_REQUESTS_FOUND': 'لا توجد طلبات'
      },
      'en': {
        'REQUEST_TITLE': 'Request Title',
        'REQUEST_DATE': 'Request Date',
        'SPECIALIZATION_SCOPE': 'Specialization Scope',
        'REQUEST_TYPE': 'Request Type',
        'STATUS': 'Status',
        'ACTIONS': 'Actions',
        'NEW': 'New',
        'IN_PROCESSING': 'In Processing',
        'FINISHED': 'Finished',
        'PURCHASING': 'Purchasing',
        'SELL': 'Sell',
        'RENT_IN': 'Rent In',
        'RENT_OUT': 'Rent Out',
        'LOADING': 'Loading...',
        'NO_REQUESTS_FOUND': 'No requests found'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
